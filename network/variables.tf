################################################################################
# Global variables
################################################################################

variable "commonTagsGlobal" {
  type    = map(string)
  default = {}
}


variable "shared_services_attachment_id" {
  description = "The attachment ID for the shared services in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-0f210f3adb6a7db6d"  
}

variable "creator" {
  description = "Creator of the resources"
  type        = string
  default     = "<EMAIL>"
}


variable "tgw_et_stage_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-09e4bd787990ae4dc" 
} 

variable "tgw_shared_services_prod_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-0e6061813253ea27c" 
} 

variable "tgw_thankview_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-0ad791180291e7a70" 
} 

variable "tgw_evertrueprod_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-00f05d6358215308d" 
} 



variable "tgw_networking_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-0b6d3e70264c46ee9" 
} 


variable "tgw_et_legacy_prod_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-08a4b813a042e6d0b" 
} 


variable "tgw_et_legacy_stage_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-09f755b6b72033643" 
} 


variable "tgw_fundriver_attachment_id" {
  description = "The attachment ID for the ET stage in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-09f6ebba1f8af8a9e" 
}


variable "tgw_openflow_attachment_id" {
  description = "The attachment ID for the ET openflow in AWS Transit Gateway"
  type        = string
  default     = "tgw-attach-09b91c1dd71363b9b" 
}

#########networking-subnets########

variable "networking_subnets" {
  description = "List of subnet IDs for the networking environment"
  type        = list(string)
  default     = [
    "subnet-01cd9289b506e484f",  // Public Subnet (AZ2)
    "subnet-0a2ab1c2ee40fe435",  // Private Subnet (AZ1)

    #"subnet-0f7cb1e04a707b266",  // Public Subnet (AZ1)
    #"subnet-0ac1b0e2ebc2a3338",  // Private Subnet (AZ2)
  ]
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Creator     = "<EMAIL>"
  }
}

variable "vpc_id" {
  type = string
  default = "vpc-032793a48807b1531"
}