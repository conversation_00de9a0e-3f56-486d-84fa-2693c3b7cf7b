####################shared-services-tgw-rt################################

resource "aws_ec2_transit_gateway_route_table" "shared_services_stage_twg_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "shared_services_stage_twg_rt"
    Creator      = var.creator
    CreationDate = "03/08/2023"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "shared_services_rt_association" {
  transit_gateway_attachment_id  = var.shared_services_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.shared_services_stage_twg_rt.id
}



####################et-stage-tgw-rt################################

resource "aws_ec2_transit_gateway_route_table" "et_stage_twg_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "et_stage_twg_rt"
    Creator      = var.creator
    CreationDate = "03/11/2023"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "et_stage_rt_association" {
  transit_gateway_attachment_id  = var.tgw_et_stage_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.et_stage_twg_rt.id
}


####################et-shared-services-prod-tgw-rt################################ - do over

resource "aws_ec2_transit_gateway_route_table" "et_shared_services_prod_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "et_shared_services_prod_tgw_rt"
    Creator      = var.creator
    CreationDate = "05/16/2023"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "et_shared_services_prod_association" {
  transit_gateway_attachment_id  = var.tgw_shared_services_prod_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.et_shared_services_prod_rt.id
}


####################et-thankview-tgw-rt################################ - do over

resource "aws_ec2_transit_gateway_route_table" "et_thankview_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "et_thankview_tgw_rt"
    Creator      = var.creator
    CreationDate = "05/16/2023"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "et_thankview_association" {
  transit_gateway_attachment_id  = var.tgw_thankview_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.et_thankview_rt.id
}

####################evertrueprod-tgw-rt################################

resource "aws_ec2_transit_gateway_route_table" "evertrueprod_tgw_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "evertrueprod_tgw_rt"
    Creator      = var.creator
    CreationDate = "05/17/2023"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "evertrueprod_tgw_association" {
  transit_gateway_attachment_id  = var.tgw_evertrueprod_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.evertrueprod_tgw_rt.id
}


####################networking-tgw-rt################################

resource "aws_ec2_transit_gateway_route_table" "networking_tgw_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "networking_tgw_rt"
    Creator      = var.creator
    CreationDate = "02/21/2025"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "networking_tgw_association" {
  transit_gateway_attachment_id  = var.tgw_networking_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.networking_tgw_rt.id
}



####################et-legacy-tgw-rt################################

resource "aws_ec2_transit_gateway_route_table" "et_legacy_prod_tgw_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "et_legacy_prod_tgw_rt"
    Creator      = var.creator
    CreationDate = "02/24/2025"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "et_legacy_prod_tgw_association" {
  transit_gateway_attachment_id  = var.tgw_et_legacy_prod_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.et_legacy_prod_tgw_rt.id
}


resource "aws_ec2_transit_gateway_route_table" "et_legacy_stage_tgw_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "et_legacy_stage_tgw_rt"
    Creator      = var.creator
    CreationDate = "02/24/2025"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "et_legacy_stage_tgw_association" {
  transit_gateway_attachment_id  = var.tgw_et_legacy_stage_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.et_legacy_stage_tgw_rt.id
}


####################fundriver-tgw-rt################################

resource "aws_ec2_transit_gateway_route_table" "et_fundriver_tgw_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "et_fundriver_tgw_rt"
    Creator      = var.creator
    CreationDate = "04/17/2025"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "et_fundriver_tgw_association" {
  transit_gateway_attachment_id  = var.tgw_fundriver_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.et_fundriver_tgw_rt.id
}



####################openflow-tgw-rt################################

resource "aws_ec2_transit_gateway_route_table" "et_openflow_tgw_rt" {
  transit_gateway_id = aws_ec2_transit_gateway.et-tgw.id

  tags = {
    Name = "et_openflow_tgw_rt"
    Creator      = var.creator
    CreationDate = "09/02/2025"
  }
}

resource "aws_ec2_transit_gateway_route_table_association" "et_openflow_tgw_association" {
  transit_gateway_attachment_id  = var.tgw_openflow_attachment_id
  transit_gateway_route_table_id = aws_ec2_transit_gateway_route_table.et_openflow_tgw_rt.id
}