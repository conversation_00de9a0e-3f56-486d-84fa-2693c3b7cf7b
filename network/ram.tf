##############et-shared-services-tgw-share##################

resource "aws_ram_resource_share" "tgw_shared_servces_share" {
  name                      = "TGW_SharedServices_Share"
  allow_external_principals = true

  tags = {
    Name    = "TGW_SharedServices_Share"
    Creator = "<EMAIL>"
  }
}


resource "aws_ram_resource_association" "tgw_share_association" {
  resource_share_arn = aws_ram_resource_share.tgw_shared_servces_share.arn
  resource_arn       = aws_ec2_transit_gateway.et-tgw.arn # ARN of the Transit Gateway
}

resource "aws_ram_principal_association" "tgw_shared_services_association" {
  resource_share_arn = aws_ram_resource_share.tgw_shared_servces_share.arn
  principal          = "************" # Your AWS account ID
}

##############et-stage-tgw-share##################

resource "aws_ram_resource_share" "tgw_et_stage_share" {
  name                      = "TGW_et_stage_Share"
  allow_external_principals = true

  tags = {
    Name    = "TGW_et_stage_Share"
    Creator = "<EMAIL>"
  }
}


resource "aws_ram_resource_association" "tgw_etstage_association" {
  resource_share_arn = aws_ram_resource_share.tgw_et_stage_share.arn
  resource_arn       = aws_ec2_transit_gateway.et-tgw.arn
}

resource "aws_ram_principal_association" "tgw_et_stage_association" {
  resource_share_arn = aws_ram_resource_share.tgw_et_stage_share.arn
  principal          = "************" # Your AWS account ID

}

#########################################################################

##############thankview-tgw-share##################

resource "aws_ram_resource_share" "tgw_thankview_share" {
  name                      = "TGW_thankview_Share"
  allow_external_principals = true

  tags = {
    Name    = "TGW_thankview_Share"
    Creator = "<EMAIL>"
  }
}


resource "aws_ram_resource_association" "tgw_thankview_association" {
  resource_share_arn = aws_ram_resource_share.tgw_thankview_share.arn
  resource_arn       = aws_ec2_transit_gateway.et-tgw.arn
}

resource "aws_ram_principal_association" "tgw_thankview__association" {
  resource_share_arn = aws_ram_resource_share.tgw_thankview_share.arn
  principal          = "************" # Your AWS account ID

}


##############SharedServicesProd-tgw-share##################

resource "aws_ram_resource_share" "tgw_shared_services_prod_share" {
  name                      = "tgw_shared_services_prod_share"
  allow_external_principals = true

  tags = {
    Name    = "tgw_shared_services_prod_share"
    Creator = "<EMAIL>"
  }
}


resource "aws_ram_resource_association" "tgw_shared_services_prod_association" {
  resource_share_arn = aws_ram_resource_share.tgw_shared_services_prod_share.arn
  resource_arn       = aws_ec2_transit_gateway.et-tgw.arn
}

resource "aws_ram_principal_association" "tgw_shared_services_prod__association" {
  resource_share_arn = aws_ram_resource_share.tgw_shared_services_prod_share.arn
  principal          = "************" # Your AWS account ID

}

##############EvertrueProd-tgw-share##################

resource "aws_ram_resource_share" "tgw_evertrue_prod_share" {
  name                      = "TGW_evertrue_prod_share"
  allow_external_principals = true

  tags = {
    Name    = "tgw_evertrue_prod_share"
    Creator = "<EMAIL>"
  }
}


resource "aws_ram_resource_association" "tgw_evertrue_prod_association" {
  resource_share_arn = aws_ram_resource_share.tgw_evertrue_prod_share.arn
  resource_arn       = aws_ec2_transit_gateway.et-tgw.arn
}

resource "aws_ram_principal_association" "tgw_evertrue_prod__association" {
  resource_share_arn = aws_ram_resource_share.tgw_evertrue_prod_share.arn
  principal          = "************" # Your AWS account ID

}


##############EvertrueLegacy-tgw-share##################

resource "aws_ram_resource_share" "tgw_evertrue_legacy_share" {
  name                      = "TGW_evertrue_legacy_share"
  allow_external_principals = true

  tags = {
    Name    = "tgw_evertrue_legacy_share"
    Creator = "<EMAIL>"
  }
}


resource "aws_ram_resource_association" "tgw_evertrue_legacy_association" {
  resource_share_arn = aws_ram_resource_share.tgw_evertrue_legacy_share.arn
  resource_arn       = aws_ec2_transit_gateway.et-tgw.arn
}

resource "aws_ram_principal_association" "tgw_evertrue_legacy__association" {
  resource_share_arn = aws_ram_resource_share.tgw_evertrue_legacy_share.arn
  principal          = "************" # Your AWS account ID

}




##############Fundriver-tgw-share##################

resource "aws_ram_resource_share" "tgw_fundriver_share" {
  name                      = "TGW_fundriver_share"
  allow_external_principals = true

  tags = {
    Name    = "tgw_fundriver_share"
    Creator = "<EMAIL>"
  }
}


resource "aws_ram_resource_association" "tgw_fundriver_association" {
  resource_share_arn = aws_ram_resource_share.tgw_fundriver_share.arn
  resource_arn       = aws_ec2_transit_gateway.et-tgw.arn
}

resource "aws_ram_principal_association" "tgw_fundriver_association" {
  resource_share_arn = aws_ram_resource_share.tgw_fundriver_share.arn
  principal          = "************" # Your AWS account ID

}

