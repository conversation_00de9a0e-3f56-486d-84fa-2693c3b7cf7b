locals {
  # CIDRs & Allocations
  account_cidrs = {
    "Evertrue" = {
      cidr         = "*********/12"
      ipam_pool_id = aws_vpc_ipam_pool.account["Evertrue Pool"].id
    }
    "Pledgemine" = {
      cidr         = "*********/12"
      ipam_pool_id = aws_vpc_ipam_pool.account["Pledgemine Pool"].id
    }
    "Thankview" = {
      cidr         = "*********/12"
      ipam_pool_id = aws_vpc_ipam_pool.account["Thankview Pool"].id
    }
    "Fundriver" = {
      cidr         = "*********/12"
      ipam_pool_id = aws_vpc_ipam_pool.account["Fundriver Pool"].id
    }
    "Miscellaneous" = {
      cidr         = "*********/12"
      ipam_pool_id = aws_vpc_ipam_pool.account["Miscellaneous Pool"].id
    }
  }
  region_cidrs = {
    "PM - us-east-1" = {
      cidr         = "*********/13"
      ipam_pool_id = aws_vpc_ipam_pool.region["PM - us-east-1"].id
    }
    "PM - us-west-2" = {
      cidr         = "*********/14"
      ipam_pool_id = aws_vpc_ipam_pool.region["PM - us-west-2"].id
    }
    "MISC - us-east-1" = {
      cidr         = "*********/14"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-east-1"].id
    }
    "MISC - us-east-2" = {
      cidr         = "*********/14"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-east-2"].id
    }
    "MISC - us-west-2" = {
      cidr         = "*********/14"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-west-2"].id
    }
  }
  vpc_allocations = {
    "PM - us-east-1 - Dev" = {
      cidr         = "*********/18"
      ipam_pool_id = aws_vpc_ipam_pool.region["PM - us-east-1"].id
      description  = "PM - us-east-1 - Dev"
    }
    "PM - us-east-1 - Stage" = {
      cidr         = "**********/18"
      ipam_pool_id = aws_vpc_ipam_pool.region["PM - us-east-1"].id
      description  = "PM - us-east-1 - Stage"
    }
    "PM - us-east-1 - Prod" = {
      cidr         = "***********/18"
      ipam_pool_id = aws_vpc_ipam_pool.region["PM - us-east-1"].id
      description  = "PM - us-east-1 - Prod"
    }
    "MISC - us-east-1 - Nango - Prod" = {
      cidr         = "*********/24"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-east-1"].id
      description  = "MISC - us-east-1 - Nango - Prod"
    }
    "MISC - us-east-1 - SharedServices - Prod" = {
      cidr         = "*********/16"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-east-1"].id
      description  = "MISC - us-east-1 - SharedServices - Prod"
    }
    "MISC - us-east-1 - SharedServices - Prod2" = {
      cidr         = "*********/16"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-east-1"].id
      description  = "MISC - us-east-1 - SharedServices - Prod2"
    }
    "MISC - us-east-1 - Network" = {
      cidr         = "*********/24"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-east-1"].id
      description  = "MISC - us-east-1 - Network"
    }
    "TV - us-east-1 - Stage" = {
      cidr         = "**********/18"
      ipam_pool_id = aws_vpc_ipam_pool.region["TV - us-east-1"].id
      description  = "TV - us-east-1 - Stage"
    }
    "MISC - us-east-1 - salesforce-migration-SharedServices - Prod" = {
      cidr         = "*********/22"
      ipam_pool_id = aws_vpc_ipam_pool.region["MISC - us-east-1"].id
      description  = "MISC - us-east-1 - salesforce-migration-SharedServices - Prod"
    }
  }
}

resource "aws_vpc_ipam_pool_cidr" "account" {
  for_each = local.account_cidrs

  ipam_pool_id = try(each.value.ipam_pool_id, "")
  cidr         = try(each.value.cidr, "")
}

resource "aws_vpc_ipam_pool_cidr" "region" {
  for_each = local.region_cidrs

  ipam_pool_id = try(each.value.ipam_pool_id, "")
  cidr         = try(each.value.cidr, "")
}

resource "aws_vpc_ipam_pool_cidr_allocation" "vpc" {
  for_each = local.vpc_allocations

  description  = try(each.value.description, "")
  ipam_pool_id = try(each.value.ipam_pool_id, "")
  cidr         = try(each.value.cidr, "")
}


