locals {
  # ensure current provider region is an operating_regions entry
  all_ipam_regions = distinct(concat([data.aws_region.current.name], tolist(["us-east-1", "us-east-2", "us-west-2", "ca-central-1"])))
  ipams = {
    "Overall Plan" = {
      description = "Ideal State"
      tags = {
        Name = "Overall Plan"
      }
    }
  }
  scopes = {
    private = {
      ipam_id     = aws_vpc_ipam.main.id
      description = "Private Networks for VPC's"
    }
    public = {
      ipam_id     = aws_vpc_ipam.main.id
      description = ""
    }
  }

  # Pools
  org_pools = {
    "Organizational Pool" = {
      description    = "Pool of IP blocks for all resources"
      address_family = "ipv4"
      locale         = null
      ipam_scope_id  = aws_vpc_ipam_scope.main["private"].id
      tags = {
        Name = "Organizational Pool"
      }
    }
  }
  account_pools = {
    "Evertrue Pool" = {
      description         = "Pool for all things Evertrue (the product)"
      address_family      = "ipv4"
      locale              = null
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.main.id
      tags = {
        Name = "Evertrue Pool"
      }
    }
    "Pledgemine Pool" = {
      description         = "Pool for all things Pledgemine (the product)"
      address_family      = "ipv4"
      locale              = null
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.main.id
      tags = {
        Name = "Pledgemine Pool"
      }
    }
    "Thankview Pool" = {
      description         = "Pool for all things ThankView (the product)"
      address_family      = "ipv4"
      locale              = null
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.main.id
      tags = {
        Name = "Thankview Pool"
      }
    }
    "Fundriver Pool" = {
      description         = "Pool for all things Fundriver (the product)"
      address_family      = "ipv4"
      locale              = null
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.main.id
      tags = {
        Name = "Fundriver Pool"
      }
    }
    "Miscellaneous Pool" = {
      description         = "Pool for all things for small projects or products"
      address_family      = "ipv4"
      locale              = null
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.main.id
      tags = {
        Name = "Miscellaneous Pool"
      }
    }
  }
  region_pools = {
    # Evertrue
    "ET - us-east-1" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-east-1"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Evertrue Pool"].id
      tags = {
        Name = "ET - us-east-1"
      }
    }
    "ET - us-west-2" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-west-2"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Evertrue Pool"].id
      tags = {
        Name = "ET - us-west-2"
      }
    }
    # Miscellaneous
    "MISC - us-east-1" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-east-1"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Miscellaneous Pool"].id
      tags = {
        Name = "MISC - us-east-1"
      }
    }
    "MISC - us-east-2" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-east-2"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Miscellaneous Pool"].id
      tags = {
        Name = "MISC - us-east-2"
      }
    }
    "MISC - us-west-2" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-west-2"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Miscellaneous Pool"].id
      tags = {
        Name = "MISC - us-west-2"
      }
    }

    # Pledgemine
    "PM - us-east-1" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-east-1"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Pledgemine Pool"].id
      tags = {
        Name = "PM - us-east-1"
      }
    }
    "PM - us-west-2" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-west-2"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Pledgemine Pool"].id
      tags = {
        Name = "PM - us-west-2"
      }
    }
    # Thankview
    "TV - us-east-1" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-east-1"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Thankview Pool"].id
      tags = {
        Name = "TV - us-east-1"
      }
    }
    "TV - ca-central-1" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "ca-central-1"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Thankview Pool"].id
      tags = {
        Name = "TV - ca-central-1"
      }
    }
    "TV - us-east-2" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-east-2"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Thankview Pool"].id
      tags = {
        Name = "TV - us-east-2"
      }
    }
    "TV - us-west-2" = {
      description         = ""
      address_family      = "ipv4"
      locale              = "us-west-2"
      ipam_scope_id       = aws_vpc_ipam_scope.main["private"].id
      source_ipam_pool_id = aws_vpc_ipam_pool.account["Thankview Pool"].id
      tags = {
        Name = "TV - us-west-2"
      }
    }
    # "" = {

    # }
  }
}

resource "aws_vpc_ipam" "main" {
  description = local.ipams["Overall Plan"].description
  dynamic "operating_regions" {
    for_each = local.all_ipam_regions
    content {
      region_name = operating_regions.value
    }
  }
  tags = local.ipams["Overall Plan"].tags
}

resource "aws_vpc_ipam_scope" "main" {
  for_each = local.scopes

  ipam_id     = try(each.value.ipam_id, "")
  description = try(each.value.description, "")
  tags        = try(each.value.tag, {})
}

resource "aws_vpc_ipam_pool" "main" {
  description    = local.org_pools["Organizational Pool"].description
  address_family = local.org_pools["Organizational Pool"].address_family
  locale         = local.org_pools["Organizational Pool"].locale
  ipam_scope_id  = local.org_pools["Organizational Pool"].ipam_scope_id
  tags           = local.org_pools["Organizational Pool"].tags
}

resource "aws_vpc_ipam_pool" "account" {
  for_each = local.account_pools

  description           = try(each.value.description, "")
  address_family        = try(each.value.address_family, "ipv4")
  locale                = try(each.value.locale, null)
  ipam_scope_id         = try(each.value.ipam_scope_id, "")
  source_ipam_pool_id   = try(each.value.source_ipam_pool_id, null)
  publicly_advertisable = try(each.value.publicly_advertisable, false)
  tags                  = try(each.value.tags, {})
}

resource "aws_vpc_ipam_pool" "region" {
  for_each = local.region_pools

  description           = try(each.value.description, "")
  auto_import           = try(each.value.auto_import, true)
  address_family        = try(each.value.address_family, "ipv4")
  locale                = try(each.value.locale, null)
  ipam_scope_id         = try(each.value.ipam_scope_id, "")
  source_ipam_pool_id   = try(each.value.source_ipam_pool_id, null)
  publicly_advertisable = try(each.value.publicly_advertisable, false)
  tags                  = try(each.value.tags, {})
}
