#permission taken from https://help.vanta.com/hc/en-us/articles/4411799148692-Connecting-AWS-as-a-Cloud-Provider


resource "aws_iam_policy" "VantaAdditionalPermissions" {
  name        = "VantaAdditionalPermissions"
  description = "Your policy description"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Deny",
      "Action": [
        "datapipeline:EvaluateExpression",
        "datapipeline:QueryObjects",
        "rds:DownloadDBLogFilePortion"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "codecommit:GetApprovalRuleTemplate"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "identitystore:Describe*",
        "identitystore:Get*",
        "identitystore:IsMemberInGroups",
        "identitystore:List*"
      ],
      "Resource": "*"
    }
  ]
}
EOF

  tags = {
    terraform = "Managed by Terraform"
  }
}